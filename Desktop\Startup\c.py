import re
import pandas as pd
import PyPDF2

# PDF path
pdf_path = "C:/Users/<USER>/Desktop/PDF to Excel/b.pdf"  # Replace with your actual path

# Read PDF content
with open(pdf_path, "rb") as file:
    reader = PyPDF2.PdfReader(file)
    text = "\n".join(
        page.extract_text() for page in reader.pages if page.extract_text()
    )

# Define the regex pattern for each entry
pattern = re.compile(
    r"Company name\s*:\s*(.*?)\s*"
    r"Contact\s*:\s*(.*?)\s*"
    r"Designation\s*:\s*(.*?)\s*"
    r"E-?mail\s*:\s*(.*?)\s*"
    r"Website\s*:\s*(.*?)\s*"
    r"Sector\s*:\s*(.*?)\s*"
    r"Address\s*:\s*(.*?)(?=Company name\s*:|$)",
    re.DOTALL | re.IGNORECASE
)

# Extract matches
matches = pattern.findall(text)

# Clean matches and prepare DataFrame
data = [list(map(str.strip, match)) for match in matches]
columns = ["Company Name", "Contact", "Designation", "E-mail", "Website", "Sector", "Address"]
df = pd.DataFrame(data, columns=columns)

# Save to Excel
output_file = "Extracted_Company_Details.xlsx"
df.to_excel(output_file, index=False)

print(f"Saved successfully to {output_file}")
