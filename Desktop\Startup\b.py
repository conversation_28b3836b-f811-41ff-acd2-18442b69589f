import re
import pandas as pd
import PyPDF2
import openpyxl

# Function to clean text and remove illegal characters
def clean_text(text):
    return re.sub(r"[^\x20-\x7E]", "", text)  # Remove non-printable ASCII characters

# Path to the PDF file
pdf_path = "C:/Users/<USER>/Desktop/PDF to Excel/b.pdf"  # Replace with your actual PDF file path

# Reading the PDF file
with open(pdf_path, "rb") as file:
    reader = PyPDF2.PdfReader(file)
    full_text = "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])

# Regex pattern to extract exhibitor details
exhibitor_pattern = re.compile(
    r"Name of the Exhibitor:\s*(.*?)\n"
    r"Address\s*:\s*(.*?)\n"
    r"Contact Person\s*:\s*(.*?)\n"
    r"Designation\s*:\s*(.*?)\n"
    r"Mobile\s*:\s*(.*?)\n"
    r"Email\s*:\s*(.*?)\n"
    r"Website\s*:\s*(.*?)\n"
    r"Sector:\s*(.*?)\n"
    r"Profile:\s*(.*?)\n",
    re.DOTALL
)

# Extracting matches
matches = exhibitor_pattern.findall(full_text)

# Cleaning extracted data
exhibitor_data = [[clean_text(field) for field in match] for match in matches]

# Define column names
columns = ["Name of Exhibitor", "Address", "Contact Person", "Designation", "Mobile", "Email", "Website", "Sector", "Profile"]

# Creating a dataframe
df_exhibitors = pd.DataFrame(exhibitor_data, columns=columns)

# Saving the cleaned data to an Excel file
output_file = "Exhibitor_Directory.xlsx"
df_exhibitors.to_excel(output_file, index=False)

print(f"Excel file saved successfully: {output_file}")
